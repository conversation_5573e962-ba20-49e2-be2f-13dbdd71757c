//
//  GameScene.swift
//  SpriteSheetTestGame4
//
//  Created by <PERSON><PERSON> on 2025-06-14.
//

import SpriteKit
import GameplayKit

enum WalkDirection {
    case left, right
}

class GameScene: SKScene {

    private var label : SKLabelNode?
    private var spinnyNode : SKShapeNode?
    private var boySprite: SKSpriteNode?
    private var walkRightAnimator: SpriteSheetAnimator?
    private var walkLeftAnimator: SpriteSheetAnimator?
    private var isWalkingLeft = false
    private var isAnimating = false
    
    override func didMove(to view: SKView) {

        // Get label node from scene and store it for use later
        self.label = self.childNode(withName: "//helloLabel") as? SKLabelNode
        if let label = self.label {
            label.alpha = 0.0
            label.run(SKAction.fadeIn(withDuration: 2.0))
        }

        // Create shape node to use during mouse interaction
        let w = (self.size.width + self.size.height) * 0.05
        self.spinnyNode = SKShapeNode.init(rectOf: CGSize.init(width: w, height: w), cornerRadius: w * 0.3)

        if let spinnyNode = self.spinnyNode {
            spinnyNode.lineWidth = 2.5

            spinnyNode.run(SKAction.repeatForever(SKAction.rotate(byAngle: CGFloat(Double.pi), duration: 1)))
            spinnyNode.run(SKAction.sequence([SKAction.wait(forDuration: 0.5),
                                              SKAction.fadeOut(withDuration: 0.5),
                                              SKAction.removeFromParent()]))
        }

        // Setup boy character sprite sheet animation
        setupBoyCharacter()

        // Setup swipe gestures
        setupSwipeGestures()
    }
    
    
    func touchDown(atPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.green
            self.addChild(n)
        }
    }
    
    func touchMoved(toPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.blue
            self.addChild(n)
        }
    }
    
    func touchUp(atPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.red
            self.addChild(n)
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let label = self.label {
            label.run(SKAction.init(named: "Pulse")!, withKey: "fadeInOut")
        }

        // Toggle boy animation on touch
        toggleAnimation()

        for t in touches { self.touchDown(atPoint: t.location(in: self)) }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchMoved(toPoint: t.location(in: self)) }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchUp(atPoint: t.location(in: self)) }
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchUp(atPoint: t.location(in: self)) }
    }
    
    
    override func update(_ currentTime: TimeInterval) {
        // Called before each frame is rendered
    }

    // MARK: - Gesture Setup
    private func setupSwipeGestures() {
        guard let view = self.view else { return }

        // Left swipe gesture
        let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(handleSwipe(_:)))
        swipeLeft.direction = .left
        view.addGestureRecognizer(swipeLeft)

        // Right swipe gesture
        let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(handleSwipe(_:)))
        swipeRight.direction = .right
        view.addGestureRecognizer(swipeRight)
    }

    @objc private func handleSwipe(_ gesture: UISwipeGestureRecognizer) {
        switch gesture.direction {
        case .left:
            if !isWalkingLeft {
                startWalkingAnimation(direction: .left)
                isAnimating = true
            }
        case .right:
            if isWalkingLeft {
                startWalkingAnimation(direction: .right)
                isAnimating = true
            }
        default:
            break
        }
    }

    // MARK: - Boy Character Setup and Animation
    private func setupBoyCharacter() {
        // Initialize both sprite sheet animators
        // Assuming the sprite sheets have frames in a single row
        // You may need to adjust these values based on your actual sprite sheet dimensions
        walkRightAnimator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 100, height: 144), // Adjust based on your sprite sheet
            framesPerRow: 6, // Adjust based on your sprite sheet layout
            totalFrames: 6   // Adjust based on total number of frames
        )

        walkLeftAnimator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkLeft_Sheet",
            frameSize: CGSize(width: 100, height: 144), // Adjust based on your sprite sheet
            framesPerRow: 6, // Adjust based on your sprite sheet layout
            totalFrames: 6   // Adjust based on total number of frames
        )

        guard let rightAnimator = walkRightAnimator,
              let _ = walkLeftAnimator,
              let firstTexture = rightAnimator.getFirstTexture() else {
            print("Error: Could not initialize sprite sheet animators")
            return
        }

        // Create the boy sprite with the first frame (facing right initially)
        boySprite = SKSpriteNode(texture: firstTexture)
        boySprite?.position = CGPoint(x: frame.midX, y: frame.midY - 100)
        boySprite?.setScale(2.0) // Make it bigger for visibility

        if let boySprite = boySprite {
            addChild(boySprite)
        }

        // Start the walking right animation
        startWalkingAnimation(direction: .right)
        isAnimating = true

        // Add instruction label
        addInstructionLabel()
    }

    private func startWalkingAnimation(direction: WalkDirection) {
        guard let boySprite = boySprite else { return }

        // Stop any existing animation
        boySprite.removeAction(forKey: "walking")

        let animator: SpriteSheetAnimator?
        switch direction {
        case .right:
            animator = walkRightAnimator
            isWalkingLeft = false
        case .left:
            animator = walkLeftAnimator
            isWalkingLeft = true
        }

        guard let selectedAnimator = animator else { return }

        let walkingAnimation = selectedAnimator.createAnimation(duration: 0.8, repeatForever: true)
        boySprite.run(walkingAnimation, withKey: "walking")

        print("Started walking \(direction == .left ? "left" : "right")")
    }

    private func stopWalkingAnimation() {
        boySprite?.removeAction(forKey: "walking")
        isAnimating = false
        print("Animation stopped")
    }

    private func toggleAnimation() {
        guard let boySprite = boySprite else { return }

        if boySprite.action(forKey: "walking") != nil {
            stopWalkingAnimation()
        } else {
            let direction: WalkDirection = isWalkingLeft ? .left : .right
            startWalkingAnimation(direction: direction)
            isAnimating = true
            print("Animation resumed")
        }
    }

    private func switchDirection() {
        guard isAnimating else { return }

        let newDirection: WalkDirection = isWalkingLeft ? .right : .left
        startWalkingAnimation(direction: newDirection)
    }

    private func addInstructionLabel() {
        let instructionLabel = SKLabelNode(fontNamed: "Arial")
        instructionLabel.text = "Tap: Toggle Animation | Swipe Left/Right: Change Direction"
        instructionLabel.fontSize = 16
        instructionLabel.fontColor = .white
        instructionLabel.position = CGPoint(x: frame.midX, y: frame.maxY - 50)
        addChild(instructionLabel)
    }
}
